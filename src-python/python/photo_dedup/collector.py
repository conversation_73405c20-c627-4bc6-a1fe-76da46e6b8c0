"""
Photo collection functionality for Tauri v2 + PyO3 thumbnail manager.
"""

import json
import logging
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

try:
    import osxphotos
except ImportError:
    osxphotos = None

from .cache import cache_result
from .database import (
    get_or_create_library,
    initialize_database,
    store_photo_info,
    get_cached_date_range,
    get_cached_photos_in_date_range,
    is_date_range_cached,
    update_date_range_cache,
    get_next_date_range_to_fetch,
    load_cached_photos,
)
from .models import PhotoInfo, ProgressCallback

logger = logging.getLogger(__name__)

# 定义并发处理的照片数量
MAX_WORKERS = 4

# 全局 PhotosDB 实例管理
_global_photosdb = None
_global_library_path = None


def _ensure_timezone_aware(dt):
    """确保datetime对象有时区信息"""
    if dt is None:
        return datetime.now(tz=timezone.utc)
    if isinstance(dt, datetime) and dt.tzinfo is None:
        return dt.replace(tzinfo=timezone.utc)
    return dt


def get_global_photosdb(library_path: str):
    """
    获取全局唯一的PhotosDB实例

    Args:
        library_path: Photos库路径

    Returns:
        PhotosDB实例
    """
    global _global_photosdb, _global_library_path

    # 如果路径改变或者还没有初始化，重新创建
    if _global_photosdb is None or _global_library_path != library_path:
        logger.info(f"🔄 创建新的PhotosDB实例: {library_path}")

        # 设置环境变量来跳过版本检查
        os.environ["OSXPHOTOS_IGNORE_VERSION_CHECK"] = "1"

        try:
            _global_photosdb = osxphotos.PhotosDB(library_path)
            _global_library_path = library_path
            logger.info("✅ 全局PhotosDB实例创建成功")
        except Exception as e:
            logger.error(f"❌ 创建PhotosDB实例失败: {e}")
            raise
    else:
        logger.info("♻️  使用现有的全局PhotosDB实例")

    return _global_photosdb


def close_global_photosdb():
    """关闭全局PhotosDB实例"""
    global _global_photosdb, _global_library_path
    if _global_photosdb is not None:
        logger.info("🔒 关闭全局PhotosDB实例")
        # PhotosDB没有显式的close方法，只需要清空引用
        _global_photosdb = None
        _global_library_path = None


def collect_photos_with_smart_cache_by_date_range(
    library_path: str,
    start_date: int,  # Unix timestamp (seconds)
    end_date: int,    # Unix timestamp (seconds)
    max_photos: int = 0,
    db_path: Optional[str] = None,
    progress_callback: Optional[Callable] = None,
) -> List[PhotoInfo]:
    """
    智能缓存照片收集（按日期范围）：优先从数据库加载，如果缓存中没有则从系统相册数据库获取并缓存

    Args:
        library_path: Photos库路径
        start_date: 开始时间戳（Unix秒）
        end_date: 结束时间戳（Unix秒）
        max_photos: 最大照片数量限制（0表示不限制）
        db_path: 数据库路径
        progress_callback: 进度回调函数

    Returns:
        照片信息列表
    """
    from datetime import datetime, timezone

    logger.info(f"🧠 智能缓存照片收集开始: {library_path}")
    logger.info(f"📅 请求日期范围: {datetime.fromtimestamp(start_date, tz=timezone.utc)} 到 {datetime.fromtimestamp(end_date, tz=timezone.utc)}")

    # 初始化数据库
    Session = initialize_database(db_path)
    session = Session()

    try:
        # 获取或创建库记录
        library = get_or_create_library(
            session,
            name=os.path.basename(library_path),
            path=library_path,
            library_type="apple_photos"
        )

        # 1. 检查缓存中是否已有请求日期范围的照片
        start_datetime = datetime.fromtimestamp(start_date, tz=timezone.utc)
        end_datetime = datetime.fromtimestamp(end_date, tz=timezone.utc)

        # 检查日期范围是否已缓存
        is_cached = is_date_range_cached(session, library_path, start_datetime, end_datetime)

        if is_cached:
            # 从缓存中获取指定日期范围的照片
            cached_range_photos = get_cached_photos_in_date_range(session, library_path, start_datetime, end_datetime)
            logger.info(f"📚 从缓存中找到 {len(cached_range_photos)} 张指定日期范围的照片")

            # 应用数量限制
            if max_photos > 0 and len(cached_range_photos) > max_photos:
                cached_range_photos = cached_range_photos[:max_photos]
                logger.info(f"限制缓存照片数量为 {max_photos} 张")

            # 转换为字典格式以便JSON序列化
            cached_photos_dict = [photo.to_dict() for photo in cached_range_photos]

            # 如果日期范围已缓存，直接返回结果（即使是空的）
            logger.info(f"✅ 日期范围已缓存，直接返回 {len(cached_photos_dict)} 张照片")
            return cached_photos_dict
        else:
            logger.info(f"📚 缓存中没有完整的日期范围数据，需要从系统相册数据库获取")

        # 2. 只有当日期范围未缓存时，才从系统相册数据库获取
        logger.info(f"� 缓存中没有 {start_datetime.date()} 到 {end_datetime.date()} 的照片数据，从系统相册数据库获取")

        # 检查库路径是否存在
        if not os.path.exists(library_path):
            logger.warning(f"⚠️ Photos库路径不存在: {library_path}")
            logger.info("📚 返回空的照片列表")
            return []

        try:
            # 从系统相册数据库获取新照片
            new_photos = collect_photos_by_date_range(
                library_path=library_path,
                start_date=start_date,
                end_date=end_date,
                max_photos=max_photos,
                db_path=db_path,
                progress_callback=progress_callback
            )

            logger.info(f"🆕 从系统相册数据库获取了 {len(new_photos)} 张新照片")
        except Exception as e:
            logger.error(f"❌ 从系统相册数据库获取照片失败: {e}")
            logger.info("📚 返回空的照片列表")
            return []

        # 3. 将新照片存储到数据库
        for photo_info in new_photos:
            try:
                # 转换 PhotoInfo 对象为字典，处理日期时间
                photo_dict = photo_info.__dict__.copy()

                # 确保date_taken有时区信息后再转换为时间戳
                logger.debug(f"存储照片 {photo_info.filename}: 原始date_taken={photo_info.date_taken}, type={type(photo_info.date_taken)}")
                date_taken = _ensure_timezone_aware(photo_info.date_taken)
                logger.debug(f"存储照片 {photo_info.filename}: 处理后date_taken={date_taken}, type={type(date_taken)}")

                if isinstance(date_taken, datetime):
                    photo_dict['date_taken'] = date_taken.timestamp()
                elif isinstance(date_taken, (int, float)):
                    photo_dict['date_taken'] = date_taken
                else:
                    # 其他情况，使用默认时间戳
                    photo_dict['date_taken'] = datetime.now(tz=timezone.utc).timestamp()

                store_photo_info(session, library.id, photo_dict)
            except Exception as e:
                logger.error(f"存储照片信息失败 {photo_info.uuid}: {e}")
                import traceback
                logger.error(f"详细错误信息: {traceback.format_exc()}")

        # 4. 更新时间范围缓存
        if new_photos:
            update_date_range_cache(
                session,
                library_path,
                start_date,
                end_date,
                len(new_photos)
            )
            logger.info(f"📊 更新时间范围缓存: {start_date} 到 {end_date}")

        # 5. 合并缓存照片和新照片，确保时区一致性
        result_photos = list(cached_photos)  # 从缓存照片开始
        logger.info(f"缓存照片数量: {len(cached_photos)}")

        # 检查缓存照片的date_taken类型
        for i, photo in enumerate(cached_photos[:3]):  # 只检查前3张缓存照片
            logger.info(f"缓存照片 {i+1}: {photo.filename}, date_taken={photo.date_taken}, type={type(photo.date_taken)}, tzinfo={getattr(photo.date_taken, 'tzinfo', None)}")

        for photo in new_photos:
            # 确保新照片的date_taken有时区信息
            date_taken = _ensure_timezone_aware(photo.date_taken)
            logger.debug(f"新照片 {photo.filename}: 原始date_taken={photo.date_taken}, 处理后={date_taken}")

            # 创建新的PhotoInfo对象，确保date_taken有时区信息
            new_photo = PhotoInfo(
                uuid=photo.uuid,
                filename=photo.filename,
                original_path=photo.original_path,
                date_taken=date_taken,  # 使用处理后的date_taken
                file_size=photo.file_size,
                width=photo.width,
                height=photo.height,
                mime_type=photo.mime_type,
                thumbnail_path=photo.thumbnail_path,
                camera_model=photo.camera_model,
                location=photo.location,
                hash_values=photo.hash_values
            )
            result_photos.append(new_photo)

        logger.info(f"合并后总照片数量: {len(result_photos)}")

        # 7. 统一时区处理并按拍摄时间排序
        def safe_date_taken(photo):
            """安全获取拍摄时间，确保时区一致性"""
            try:
                date_taken = photo.date_taken
                logger.debug(f"排序照片 {photo.filename}: date_taken={date_taken}, type={type(date_taken)}, tzinfo={getattr(date_taken, 'tzinfo', None)}")

                if date_taken is None:
                    return datetime.fromtimestamp(0, tz=timezone.utc)  # 默认时间

                # 如果是naive datetime（没有时区信息），假设为UTC
                if isinstance(date_taken, datetime) and date_taken.tzinfo is None:
                    return date_taken.replace(tzinfo=timezone.utc)

                # 如果已经有时区信息，直接返回
                if isinstance(date_taken, datetime) and date_taken.tzinfo is not None:
                    return date_taken

                # 如果是时间戳，转换为UTC datetime
                if isinstance(date_taken, (int, float)):
                    return datetime.fromtimestamp(date_taken, tz=timezone.utc)

                # 其他情况，返回默认时间
                return datetime.fromtimestamp(0, tz=timezone.utc)
            except Exception as e:
                logger.error(f"排序时处理照片 {photo.filename} 的date_taken失败: {e}")
                return datetime.fromtimestamp(0, tz=timezone.utc)

        logger.info(f"开始排序 {len(result_photos)} 张照片...")

        # 在排序前，检查所有照片的date_taken类型
        for i, photo in enumerate(result_photos[:5]):  # 只检查前5张照片
            date_taken = photo.date_taken
            logger.info(f"照片 {i+1}: {photo.filename}, date_taken={date_taken}, type={type(date_taken)}, tzinfo={getattr(date_taken, 'tzinfo', None)}")

        try:
            result_photos.sort(key=safe_date_taken, reverse=True)
            logger.info(f"排序完成")
        except Exception as e:
            logger.error(f"排序照片时出错: {e}")
            # 如果排序失败，至少返回未排序的照片
            pass

        # 8. 应用数量限制
        if max_photos > 0:
            result_photos = result_photos[:max_photos]

        # 9. 转换为字典格式以便JSON序列化
        result_photos_dict = [photo.to_dict() for photo in result_photos]

        logger.info(f"✅ 智能缓存收集完成，总共 {len(result_photos_dict)} 张照片")
        return result_photos_dict

    except Exception as e:
        logger.error(f"❌ 智能缓存收集失败: {e}")
        logger.info("📚 返回空的照片列表")
        return []
    finally:
        session.close()


def process_single_photo(photo, i, db_session, library_obj, progress_callback, total):
    """处理单张照片的函数，用于并发处理"""
    logger.info(f"🔄 开始处理照片 {i + 1}: {photo.filename} (UUID: {photo.uuid})")

    if progress_callback:
        progress_callback("collecting", i + 1, total, f"Processing {photo.filename}")

    try:
        # 处理日期时区问题
        photo_date = photo.date
        if photo_date:
            # 如果日期有时区信息，转换为 naive datetime（移除时区信息）
            if photo_date.tzinfo is not None:
                photo_date = photo_date.replace(tzinfo=None)
        else:
            photo_date = datetime.now()

        # 添加更多的空值检查，处理不同的属性名
        file_size = 0
        if hasattr(photo, "filesize") and photo.filesize:
            file_size = photo.filesize
        elif hasattr(photo, "file_size") and photo.file_size:
            file_size = photo.file_size
        elif hasattr(photo, "size") and photo.size:
            file_size = photo.size

        # 处理相机型号属性
        camera_model = ""
        if hasattr(photo, "camera_model") and photo.camera_model:
            camera_model = photo.camera_model
        elif hasattr(photo, "camera_make") and photo.camera_make:
            camera_model = photo.camera_make
        elif hasattr(photo, "exif_info") and photo.exif_info:
            # 尝试从 EXIF 信息中获取相机信息
            exif = photo.exif_info
            if hasattr(exif, "camera_model"):
                camera_model = exif.camera_model or ""

        # 获取缩略图路径 - 使用多种方法尝试获取缩略图
        thumbnail_path = None

        # 方法1: 尝试使用 path_derivatives
        if hasattr(photo, "path_derivatives") and photo.path_derivatives:
            logger.info(f"🔍 照片 {photo.filename} 有 {len(photo.path_derivatives)} 个衍生文件")

            # 详细记录所有衍生文件信息
            for idx, derivative_path in enumerate(photo.path_derivatives):
                if derivative_path:
                    exists = os.path.exists(derivative_path)
                    if exists:
                        file_size = os.path.getsize(derivative_path)
                        logger.info(f"  ✅ 衍生文件 {idx + 1}: {derivative_path} (存在, {file_size} bytes)")
                    else:
                        logger.warning(f"  ❌ 衍生文件 {idx + 1}: {derivative_path} (不存在)")
                else:
                    logger.warning(f"  ❌ 衍生文件 {idx + 1}: None")

            # 选择第一个存在的衍生文件作为缩略图
            for derivative_path in photo.path_derivatives:
                if derivative_path and os.path.exists(derivative_path):
                    thumbnail_path = derivative_path
                    logger.info(f"✅ 使用衍生文件作为缩略图: {thumbnail_path}")
                    break

            if not thumbnail_path:
                logger.error(f"❌ 照片 {photo.filename} 有 {len(photo.path_derivatives)} 个衍生文件，但都不存在")
        else:
            logger.warning(f"❌ 照片 {photo.filename} 没有衍生文件 (path_derivatives: {getattr(photo, 'path_derivatives', 'N/A')})")

        # 方法2: 如果没有找到衍生文件，尝试生成缩略图
        if not thumbnail_path and hasattr(photo, "render_thumbnail"):
            logger.debug(f"尝试为照片 {photo.filename} 生成缩略图")
            try:
                thumbnail_data = photo.render_thumbnail()
                if thumbnail_data:
                    # 创建缩略图目录
                    from pathlib import Path

                    home_dir = Path.home()
                    thumbnail_dir = home_dir / ".photo_thumbnails"
                    thumbnail_dir.mkdir(exist_ok=True)

                    # 生成缩略图文件名
                    thumbnail_filename = f"{photo.uuid}_thumb.jpg"
                    thumbnail_path = str(thumbnail_dir / thumbnail_filename)

                    # 保存缩略图数据到文件
                    with open(thumbnail_path, "wb") as f:
                        f.write(thumbnail_data)

                    logger.info(f"✅ 生成缩略图: {thumbnail_path} (大小: {len(thumbnail_data)} 字节)")
                else:
                    logger.warning(f"照片 {photo.filename} 的 render_thumbnail() 返回空数据")
            except Exception as e:
                logger.warning(f"生成缩略图失败 {photo.filename}: {e}")
                thumbnail_path = None
        elif not thumbnail_path:
            logger.debug(f"照片 {photo.filename} 不支持 render_thumbnail 方法")

        # 如果没有缩略图，尝试使用原始路径（但对于iCloud照片通常为空）
        original_path = ""
        if photo.path and os.path.exists(photo.path):
            original_path = photo.path

        photo_info = PhotoInfo(
            uuid=str(photo.uuid) if photo.uuid else f"unknown_{i}",
            filename=photo.filename or f"unknown_file_{i}",
            original_path=original_path,  # 可能为空，特别是iCloud照片
            date_taken=_ensure_timezone_aware(photo_date),
            file_size=file_size,
            width=photo.width or 0,
            height=photo.height or 0,
            mime_type=photo.uti or "image/jpeg",
            thumbnail_path=thumbnail_path,  # 设置缩略图路径
            camera_model=camera_model,
            location={"latitude": photo.latitude, "longitude": photo.longitude} if photo.latitude and photo.longitude else None,
        )

        # 调试信息：记录缩略图路径状态
        if thumbnail_path:
            logger.debug(f"照片 {photo.filename} 缩略图路径: {thumbnail_path}")
        else:
            logger.warning(f"照片 {photo.filename} 没有缩略图路径")

        # Store in database if session is available
        if db_session and library_obj:
            try:
                photo_dict = photo_info.to_dict()
                # Convert location back to separate fields
                if photo_info.location:
                    photo_dict["latitude"] = photo_info.location["latitude"]
                    photo_dict["longitude"] = photo_info.location["longitude"]
                else:
                    photo_dict["latitude"] = None
                    photo_dict["longitude"] = None
                store_photo_info(db_session, library_obj.id, photo_dict)
            except Exception as e:
                logger.warning(f"Failed to store photo in database: {e}")
                # Rollback the session to recover from any transaction errors
                try:
                    db_session.rollback()
                except Exception as rollback_error:
                    logger.error(f"Failed to rollback session: {rollback_error}")

        return photo_info

    except Exception as e:
        logger.warning(f"Failed to process photo {getattr(photo, 'filename', 'unknown')}: {e}")
        return None


@cache_result(ttl=1800)  # 缓存30分钟
def collect_photos_from_library(library_path: str, days_back: int = 30, max_photos: int = 0, progress_callback: Optional[ProgressCallback] = None, db_path: str = None) -> List[PhotoInfo]:
    """
    Collect photos from Apple Photos library.

    Args:
        library_path: Path to Apple Photos library (.photoslibrary)
        days_back: Number of days back to collect photos
        max_photos: Maximum number of photos to collect
        progress_callback: Optional callback for progress reporting

    Returns:
        List of PhotoInfo objects
    """
    if osxphotos is None:
        raise ImportError("osxphotos library not available")

    if not library_path.endswith(".photoslibrary"):
        raise ValueError("Invalid Apple Photos library path")

    if not os.path.exists(library_path):
        raise FileNotFoundError(f"Library not found: {library_path}")

    try:
        # 尝试创建PhotosDB实例，添加更详细的错误处理
        logger.info(f"正在尝试打开Photos库: {library_path}")

        try:
            # 首先检查权限
            logger.info(f"正在检查Photos库权限: {library_path}")

            # 检查库文件是否存在
            if not os.path.exists(library_path):
                logger.error(f"Photos库不存在: {library_path}")
                return []

            # 检查是否可以读取库目录
            if not os.access(library_path, os.R_OK):
                logger.error(f"无法读取Photos库目录: {library_path}")
                logger.error("请在系统偏好设置 > 安全性与隐私 > 隐私 > 完全磁盘访问权限中添加此应用")
                return []

            # 检查数据库文件
            db_path_check = os.path.join(library_path, "database", "photos.db")
            if os.path.exists(db_path_check):
                if not os.access(db_path_check, os.R_OK):
                    logger.error(f"无法读取Photos数据库文件: {db_path_check}")
                    logger.error("这通常表示缺少完全磁盘访问权限")
                    return []
                else:
                    logger.info(f"Photos数据库文件可访问: {db_path_check}")

            # 使用全局PhotosDB实例
            photosdb = get_global_photosdb(library_path)
        except PermissionError as e:
            logger.error(f"权限错误：无法访问Photos库。错误: {e}")
            logger.error("解决方案：")
            logger.error("1. 打开系统偏好设置 > 安全性与隐私 > 隐私")
            logger.error("2. 选择'完全磁盘访问权限'")
            logger.error("3. 点击锁图标并输入密码")
            logger.error("4. 添加此应用到允许列表")
            logger.error("5. 重启应用")
            return []
        except Exception as e:
            logger.error(f"无法打开Photos库: {e}")
            # 检查是否是macOS版本兼容性问题
            if "WARNING: This module has only been tested with macOS versions" in str(e):
                logger.warning("检测到macOS版本兼容性警告，但尝试继续运行...")
                # 尝试忽略版本检查继续运行
                try:
                    # 使用全局PhotosDB实例（已经设置了环境变量）
                    photosdb = get_global_photosdb(library_path)
                except Exception as e2:
                    logger.error(f"即使忽略版本检查也无法打开Photos库: {e2}")
                    return []
            else:
                return []

        # 使用QueryOptions按添加时间查询最近的照片
        cutoff_date = datetime.now(tz=timezone.utc) - timedelta(days=days_back)
        current_date = datetime.now(tz=timezone.utc)

        # 使用添加时间查询最近的照片，只查询照片，排除视频和Live Photos
        options = osxphotos.QueryOptions(
            added_after=cutoff_date,
            added_before=current_date,
            photos=True,      # 只包含照片
            movies=False,     # 排除视频
            not_live=True     # 排除Live Photos
        )
        recent_photos = photosdb.query(options)

        logger.info(f"过滤后找到 {len(recent_photos)} 张最近 {days_back} 天的照片")

        # Limit to max_photos (如果max_photos > 0)
        if max_photos > 0:
            recent_photos = recent_photos[:max_photos]
            logger.info(f"限制为 {max_photos} 张照片")
        else:
            logger.info(f"不限制数量，处理所有 {len(recent_photos)} 张照片")

        photo_infos = []
        total = len(recent_photos)

        # Initialize database if path provided
        db_session = None
        library_obj = None
        if db_path:
            try:
                Session = initialize_database(db_path)
                db_session = Session()
                library_name = os.path.basename(library_path)
                library_obj = get_or_create_library(db_session, library_name, library_path, "apple_photos")
            except Exception as e:
                logger.error(f"Database initialization failed: {e}")

        # 使用线程池并发处理照片
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # 提交所有任务
            future_to_index = {executor.submit(process_single_photo, photo, i, db_session, library_obj, progress_callback, total): i for i, photo in enumerate(recent_photos)}

            # 收集结果
            for future in as_completed(future_to_index):
                photo_info = future.result()
                if photo_info:
                    photo_infos.append(photo_info)

        logger.info(f"成功处理了 {len(photo_infos)} 张照片")

        # Close database session
        if db_session:
            db_session.close()

        return photo_infos

    except Exception as e:
        logger.error(f"Error collecting photos from library: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        if hasattr(e, "__traceback__"):
            import traceback

            logger.error(f"Traceback: {traceback.format_exc()}")
        # 返回空列表而不是抛出异常，让应用继续运行
        return []


def collect_photos_from_directory(directory_path: str, extensions: List[str] = None, max_photos: int = 1000, progress_callback: Optional[ProgressCallback] = None) -> List[PhotoInfo]:
    """
    Collect photos from a directory.

    Args:
        directory_path: Path to directory containing photos
        extensions: List of file extensions to include (default: common image formats)
        max_photos: Maximum number of photos to collect
        progress_callback: Optional callback for progress reporting

    Returns:
        List of PhotoInfo objects
    """
    if extensions is None:
        extensions = [".jpg", ".jpeg", ".png", ".tiff", ".tif", ".bmp", ".gif", ".heic", ".heif"]

    directory = Path(directory_path)
    if not directory.exists():
        raise FileNotFoundError(f"Directory not found: {directory_path}")

    if not directory.is_dir():
        raise ValueError(f"Path is not a directory: {directory_path}")

    try:
        from PIL import Image
    except ImportError:
        raise ImportError("PIL/Pillow library not available")

    # Collect all image files
    image_files = []
    for ext in extensions:
        image_files.extend(directory.rglob(f"*{ext}"))
        image_files.extend(directory.rglob(f"*{ext.upper()}"))

    # Limit to max_photos
    image_files = image_files[:max_photos]

    photo_infos = []
    total = len(image_files)

    for i, image_file in enumerate(image_files):
        if progress_callback:
            progress_callback("collecting", i + 1, total, f"Processing {image_file.name}")

        try:
            # Get image metadata
            with Image.open(image_file) as img:
                width, height = img.size
                mime_type = img.format or "JPEG"

            # Get file stats
            stat = image_file.stat()

            photo_info = PhotoInfo(
                uuid=str(image_file.stem),  # Use filename as UUID for files
                filename=image_file.name,
                original_path=str(image_file.absolute()),
                date_taken=datetime.fromtimestamp(stat.st_mtime, tz=timezone.utc),
                file_size=stat.st_size,
                width=width,
                height=height,
                mime_type=f"image/{mime_type.lower()}",
            )
            photo_infos.append(photo_info)

        except Exception as e:
            logger.warning(f"Failed to process file {image_file}: {e}")
            continue

    return photo_infos


def validate_library_path(path: str) -> bool:
    """Validate if the given path is a valid photo library or directory."""
    if not os.path.exists(path):
        return False

    if path.endswith(".photoslibrary"):
        return os.path.isdir(path)

    return os.path.isdir(path)


def get_library_info(path: str) -> Dict[str, Any]:
    """Get basic information about a photo library or directory."""
    if not os.path.exists(path):
        raise FileNotFoundError(f"Path not found: {path}")

    info = {"path": path, "name": os.path.basename(path), "type": "apple_photos" if path.endswith(".photoslibrary") else "directory", "exists": True}

    if path.endswith(".photoslibrary"):
        if osxphotos:
            try:
                photosdb = get_global_photosdb(path)
                # 使用QueryOptions获取所有照片数量（避免加载所有照片到内存）
                # 查询最近10年的照片作为近似总数
                from datetime import datetime, timedelta, timezone

                ten_years_ago = datetime.now(tz=timezone.utc) - timedelta(days=3650)
                current_date = datetime.now(tz=timezone.utc)
                options = osxphotos.QueryOptions(
                    added_after=ten_years_ago,
                    added_before=current_date,
                    photos=True,      # 只包含照片
                    movies=False,     # 排除视频
                    not_live=True     # 排除Live Photos
                )
                recent_photos = photosdb.query(options)
                info["photo_count"] = len(recent_photos)
                info["last_modified"] = os.path.getmtime(path)
            except Exception as e:
                info["error"] = str(e)
                info["photo_count"] = 0
        else:
            info["error"] = "osxphotos not available"
            info["photo_count"] = 0
    else:
        # Directory scan
        try:
            image_files = []
            extensions = [".jpg", ".jpeg", ".png", ".tiff", ".tif", ".bmp", ".gif", ".heic", ".heif"]

            for ext in extensions:
                image_files.extend(list(Path(path).rglob(f"*{ext}")))
                image_files.extend(list(Path(path).rglob(f"*{ext.upper()}")))

            info["photo_count"] = len(image_files)
            info["last_modified"] = os.path.getmtime(path)
        except Exception as e:
            info["error"] = str(e)
            info["photo_count"] = 0

    return info


def collect_photos_json(library_path: str = None, directory_path: str = None, days_back: int = 30, max_photos: int = 1000) -> str:
    """
    Collect photos and return as JSON string for Rust integration.
    仅支持 Apple Photos Library.photoslibrary

    Args:
        library_path: Path to Apple Photos library (必须以.photoslibrary结尾)
        directory_path: 已弃用，不再支持普通目录
        days_back: Number of days back to collect (for Apple Photos)
        max_photos: Maximum number of photos to collect

    Returns:
        JSON string containing list of photo information
    """
    try:
        # 检查是否提供了目录路径（不再支持）
        if directory_path:
            raise ValueError("不再支持普通目录，请使用 Apple Photos Library.photoslibrary")

        # 检查是否提供了库路径
        if not library_path:
            raise ValueError("必须提供 library_path 参数")

        # 检查是否是 Photos Library
        if not library_path.endswith(".photoslibrary"):
            raise ValueError("只支持 Apple Photos Library.photoslibrary 文件")

        # 收集照片
        photos = collect_photos_from_library(library_path, days_back, max_photos)

        # Convert PhotoInfo objects to dictionaries
        photos_dict = [photo.to_dict() for photo in photos]

        # Return as JSON string
        return json.dumps(photos_dict, ensure_ascii=False, indent=None)

    except Exception as e:
        logger.error(f"Error in collect_photos_json: {e}")
        # Return empty list as JSON on error
        return json.dumps([])


def collect_photos_by_date_added_range(
    library_path: str,
    start_date: int,  # Unix timestamp (seconds)
    end_date: int,  # Unix timestamp (seconds)
    max_photos: int = 0,  # 0表示不限制数量
    db_path: Optional[str] = None,
    progress_callback: Optional[Callable] = None,
) -> List[PhotoInfo]:
    """
    按照添加时间范围收集照片（使用date_added属性）

    Args:
        library_path: Photos库路径
        start_date: 开始时间戳（Unix秒）
        end_date: 结束时间戳（Unix秒）
        max_photos: 最大照片数量，0表示不限制
        db_path: 数据库路径（可选）
        progress_callback: 进度回调函数（可选）

    Returns:
        List[PhotoInfo]: 照片信息列表
    """
    logger.info(f"按添加日期范围收集照片: {library_path}")
    logger.info(f"时间戳范围: {start_date} 到 {end_date}")

    # Convert timestamps to datetime objects with timezone awareness
    from datetime import timezone

    start_datetime = datetime.fromtimestamp(start_date, tz=timezone.utc)
    end_datetime = datetime.fromtimestamp(end_date, tz=timezone.utc)
    logger.info(f"添加日期范围: {start_datetime} 到 {end_datetime}")

    # 检查日期范围是否合理
    if start_date >= end_date:
        logger.warning(f"开始时间 {start_date} 大于等于结束时间 {end_date}")

    # 计算日期范围的时间差
    time_diff = end_datetime - start_datetime
    date_diff_days = time_diff.days
    date_diff_hours = time_diff.total_seconds() / 3600

    if date_diff_days > 0:
        logger.info(f"查找范围跨度: {date_diff_days} 天")
    else:
        logger.info(f"查找范围跨度: {date_diff_hours:.1f} 小时")

    try:
        # 检查路径权限
        logger.info(f"正在检查Photos库权限: {library_path}")
        if not os.path.exists(library_path):
            raise FileNotFoundError(f"Photos库不存在: {library_path}")

        if not os.access(library_path, os.R_OK):
            raise PermissionError(f"无法访问Photos库: {library_path}")

        # 检查是否为Photos库
        if not library_path.endswith(".photoslibrary"):
            raise ValueError("只支持 Apple Photos Library.photoslibrary 文件")

        # 检查数据库文件
        db_file = os.path.join(library_path, "database", "photos.db")
        if not os.path.exists(db_file):
            raise FileNotFoundError(f"Photos数据库文件不存在: {db_file}")

        if not os.access(db_file, os.R_OK):
            raise PermissionError(f"无法访问Photos数据库文件: {db_file}")

        logger.info(f"Photos数据库文件可访问: {db_file}")

        # 使用全局PhotosDB实例
        if osxphotos is None:
            raise ImportError("osxphotos library not available")

        photosdb = get_global_photosdb(library_path)

        # 使用QueryOptions按添加时间查询照片，只查询照片，排除视频和Live Photos
        options = osxphotos.QueryOptions(
            added_after=start_datetime,
            added_before=end_datetime,
            photos=True,      # 只包含照片
            movies=False,     # 排除视频
            not_live=True     # 排除Live Photos
        )

        # 获取指定时间段内添加的照片
        filtered_photos = photosdb.query(options)
        logger.info(f"成功打开Photos库，在指定时间段内添加的照片: {len(filtered_photos)} 张")

        # 如果设置了最大照片数量限制
        if max_photos > 0 and len(filtered_photos) > max_photos:
            # 按添加时间倒序排序（最新添加的在前）
            def safe_date_added(p):
                date_added = p.date_added
                if date_added is None:
                    return datetime.min.replace(tzinfo=timezone.utc)
                if isinstance(date_added, datetime) and date_added.tzinfo is None:
                    return date_added.replace(tzinfo=timezone.utc)
                return date_added
            filtered_photos = sorted(filtered_photos, key=safe_date_added, reverse=True)
            filtered_photos = filtered_photos[:max_photos]
            logger.info(f"限制照片数量为 {max_photos} 张")

        logger.info(f"添加日期范围内找到 {len(filtered_photos)} 张照片")

        # 如果没有找到照片，尝试扩大查询范围
        if len(filtered_photos) == 0:
            logger.info("在指定添加日期范围内没有找到照片")
            logger.info(f"查询范围: {start_datetime} 到 {end_datetime}")

            # 尝试扩大查询范围到最近30天
            logger.info("尝试查询最近30天添加的照片...")
            recent_end = datetime.now(tz=timezone.utc)
            recent_start = recent_end - timedelta(days=30)

            recent_options = osxphotos.QueryOptions(
                added_after=recent_start,
                added_before=recent_end,
                photos=True,      # 只包含照片
                movies=False,     # 排除视频
                not_live=True     # 排除Live Photos
            )

            recent_photos = photosdb.query(recent_options)
            logger.info(f"最近30天添加的照片: {len(recent_photos)} 张")

            if recent_photos and max_photos > 0:
                # 按添加时间倒序排序，取最新的照片
                def safe_date_added_recent(p):
                    date_added = p.date_added
                    if date_added is None:
                        return datetime.min.replace(tzinfo=timezone.utc)
                    if isinstance(date_added, datetime) and date_added.tzinfo is None:
                        return date_added.replace(tzinfo=timezone.utc)
                    return date_added
                recent_photos = sorted(recent_photos, key=safe_date_added_recent, reverse=True)
                filtered_photos = recent_photos[:max_photos]
                logger.info(f"使用最近添加的 {len(filtered_photos)} 张照片")

        # 处理照片信息
        photo_infos = []
        for i, photo in enumerate(filtered_photos):
            if progress_callback:
                progress_callback(i + 1, len(filtered_photos))

            logger.info(f"🔄 处理照片 {i + 1}/{len(filtered_photos)}: {photo.filename} (UUID: {photo.uuid})")

            # 获取照片的衍生文件（缩略图）
            path_derivatives = photo.path_derivatives
            logger.info(f"🔍 照片 {photo.filename} 有 {len(path_derivatives)} 个衍生文件")

            thumbnail_path = None
            if path_derivatives:
                # 选择第一个存在的衍生文件作为缩略图
                for j, derivative_path in enumerate(path_derivatives):
                    if os.path.exists(derivative_path):
                        file_size = os.path.getsize(derivative_path)
                        logger.info(f"  ✅ 衍生文件 {j + 1}: {derivative_path} (存在, {file_size} bytes)")
                        if thumbnail_path is None:
                            thumbnail_path = derivative_path
                            logger.info(f"✅ 使用衍生文件作为缩略图: {thumbnail_path}")
                    else:
                        logger.warning(f"  ❌ 衍生文件 {j + 1}: {derivative_path} (不存在)")

            # 如果没有找到衍生文件，使用原始文件路径
            if thumbnail_path is None:
                if photo.path and os.path.exists(photo.path):
                    thumbnail_path = photo.path
                    logger.info(f"⚠️  使用原始文件作为缩略图: {thumbnail_path}")
                else:
                    logger.warning(f"❌ 照片 {photo.filename} 没有可用的缩略图路径")
                    continue

            logger.info(f"✅ 照片 {photo.filename} 缩略图路径: {thumbnail_path}")

            # 创建PhotoInfo对象，使用拍摄日期而不是添加日期
            photo_info = PhotoInfo(
                uuid=photo.uuid,
                filename=photo.filename,
                original_path=photo.path or "",
                date_taken=self._ensure_timezone_aware(photo.date) if photo.date else datetime.now(tz=timezone.utc),
                file_size=photo.original_filesize or 0,
                width=photo.width or 0,
                height=photo.height or 0,
                mime_type=photo.uti or "unknown",
                thumbnail_path=thumbnail_path
            )
            photo_infos.append(photo_info)

        logger.info(f"成功处理 {len(photo_infos)} 张照片")
        return photo_infos

    except Exception as e:
        logger.error(f"收集照片时发生错误: {e}")
        import traceback

        logger.error(f"错误详情: {traceback.format_exc()}")
        return []


def collect_photos_timeline_by_added_date(
    library_path: str,
    days_per_segment: int = 5,
    max_segments: int = 10,
    max_photos_per_segment: int = 50,
    progress_callback: Optional[Callable] = None,
) -> List[PhotoInfo]:
    """
    按照添加时间的时间线方式收集照片，以指定天数为一段

    Args:
        library_path: Photos库路径
        days_per_segment: 每段的天数，默认5天
        max_segments: 最大段数，默认10段
        max_photos_per_segment: 每段最大照片数，默认50张
        progress_callback: 进度回调函数（可选）

    Returns:
        List[PhotoInfo]: 按添加时间倒序排列的照片信息列表
    """
    logger.info(f"开始按时间线收集照片: {library_path}")
    logger.info(f"每段天数: {days_per_segment}, 最大段数: {max_segments}, 每段最大照片数: {max_photos_per_segment}")

    all_photos = []
    current_date = datetime.now(tz=timezone.utc)

    for segment in range(max_segments):
        # 计算当前段的时间范围
        segment_end = current_date - timedelta(days=segment * days_per_segment)
        segment_start = segment_end - timedelta(days=days_per_segment)

        logger.info(f"📅 处理第 {segment + 1} 段: {segment_start.strftime('%Y-%m-%d')} 到 {segment_end.strftime('%Y-%m-%d')}")

        # 获取这一段的照片
        segment_photos = collect_photos_by_date_added_range(library_path=library_path, start_date=int(segment_start.timestamp()), end_date=int(segment_end.timestamp()), max_photos=max_photos_per_segment, progress_callback=progress_callback)

        if segment_photos:
            all_photos.extend(segment_photos)
            logger.info(f"✅ 第 {segment + 1} 段找到 {len(segment_photos)} 张照片")
        else:
            logger.info(f"⏹️  第 {segment + 1} 段没有照片")
            # 如果连续几段都没有照片，可以考虑提前结束
            if segment >= 2:  # 至少尝试3段
                break

    # 按添加时间倒序排序（最新添加的在前）
    # 注意：PhotoInfo对象中没有date_added字段，我们使用date_taken作为排序依据
    # 因为collect_photos_by_date_added_range已经按添加时间查询了照片
    def safe_date_taken_timeline(p):
        """安全获取拍摄时间，确保时区一致性"""
        return _ensure_timezone_aware(p.date_taken)

    all_photos.sort(key=safe_date_taken_timeline, reverse=True)

    logger.info(f"🎉 时间线收集完成，总共找到 {len(all_photos)} 张照片")
    return all_photos


def collect_photos_by_date_range(
    library_path: str,
    start_date: int,  # Unix timestamp (seconds)
    end_date: int,  # Unix timestamp (seconds)
    max_photos: int = 0,  # 0表示不限制数量
    db_path: Optional[str] = None,
    progress_callback: Optional[Callable] = None,
) -> List[PhotoInfo]:
    """
    按日期范围从Photos Library收集照片

    Args:
        library_path: Photos Library路径
        start_date: 开始日期的Unix时间戳（秒）
        end_date: 结束日期的Unix时间戳（秒）
        max_photos: 最大照片数量
        db_path: 可选的数据库路径
        progress_callback: 进度回调函数

    Returns:
        List[PhotoInfo]: 照片信息列表
    """
    logger.info(f"按日期范围收集照片: {library_path}")
    logger.info(f"时间戳范围: {start_date} 到 {end_date}")

    # Convert timestamps to datetime objects with timezone awareness
    from datetime import timezone

    start_datetime = datetime.fromtimestamp(start_date, tz=timezone.utc)
    end_datetime = datetime.fromtimestamp(end_date, tz=timezone.utc)
    logger.info(f"日期范围: {start_datetime} 到 {end_datetime}")

    # 检查日期范围是否合理
    if start_date >= end_date:
        logger.warning(f"开始时间 {start_date} 大于等于结束时间 {end_date}")

    # 检查是否是今天的日期范围
    today = datetime.now(tz=timezone.utc).date()
    if start_datetime.date() == today:
        logger.info(f"正在查找今天 ({today}) 的照片")

    # 计算日期范围的时间差
    time_diff = end_datetime - start_datetime
    date_diff_days = time_diff.days
    date_diff_hours = time_diff.total_seconds() / 3600

    if date_diff_days > 0:
        logger.info(f"查找范围跨度: {date_diff_days} 天")
    else:
        logger.info(f"查找范围跨度: {date_diff_hours:.1f} 小时")

    try:
        # 检查路径权限
        logger.info(f"正在检查Photos库权限: {library_path}")
        if not os.path.exists(library_path):
            raise FileNotFoundError(f"Photos库不存在: {library_path}")

        if not os.access(library_path, os.R_OK):
            raise PermissionError(f"无法访问Photos库: {library_path}")

        # 检查是否为Photos库
        if not library_path.endswith(".photoslibrary"):
            raise ValueError("只支持 Apple Photos Library.photoslibrary 文件")

        # 检查数据库文件
        db_file = os.path.join(library_path, "database", "photos.db")
        if not os.path.exists(db_file):
            raise FileNotFoundError(f"Photos数据库文件不存在: {db_file}")

        if not os.access(db_file, os.R_OK):
            raise PermissionError(f"无法访问Photos数据库文件: {db_file}")

        logger.info(f"Photos数据库文件可访问: {db_file}")

        # 使用全局PhotosDB实例
        if osxphotos is None:
            raise ImportError("osxphotos library not available")

        photosdb = get_global_photosdb(library_path)

        # 使用QueryOptions按添加时间查询照片，只查询照片，排除视频和Live Photos
        options = osxphotos.QueryOptions(
            added_after=start_datetime,
            added_before=end_datetime,
            photos=True,      # 只包含照片
            movies=False,     # 排除视频
            not_live=True     # 排除Live Photos
        )

        # 获取指定时间段内添加的照片
        filtered_photos = photosdb.query(options)
        logger.info(f"成功打开Photos库，在指定时间段内添加的照片: {len(filtered_photos)} 张")

        # 如果设置了最大照片数量限制
        if max_photos > 0 and len(filtered_photos) > max_photos:
            # 按添加时间倒序排序（最新添加的在前）
            def safe_date_added_filter(p):
                date_added = p.date_added
                if date_added is None:
                    return datetime.min.replace(tzinfo=timezone.utc)
                if isinstance(date_added, datetime) and date_added.tzinfo is None:
                    return date_added.replace(tzinfo=timezone.utc)
                return date_added
            filtered_photos = sorted(filtered_photos, key=safe_date_added_filter, reverse=True)
            filtered_photos = filtered_photos[:max_photos]
            logger.info(f"限制照片数量为 {max_photos} 张")

        logger.info(f"添加日期范围内找到 {len(filtered_photos)} 张照片")

        # 如果没有找到照片，尝试扩大查询范围
        if len(filtered_photos) == 0:
            logger.info("在指定添加日期范围内没有找到照片")
            logger.info(f"查询范围: {start_datetime} 到 {end_datetime}")

            # 尝试扩大查询范围到最近30天
            logger.info("尝试查询最近30天添加的照片...")
            recent_end = datetime.now(tz=timezone.utc)
            recent_start = recent_end - timedelta(days=30)

            recent_options = osxphotos.QueryOptions(
                added_after=recent_start,
                added_before=recent_end,
                photos=True,      # 只包含照片
                movies=False,     # 排除视频
                not_live=True     # 排除Live Photos
            )

            recent_photos = photosdb.query(recent_options)
            logger.info(f"最近30天添加的照片: {len(recent_photos)} 张")

            if recent_photos and max_photos > 0:
                # 按添加时间倒序排序，取最新的照片
                def safe_date_added_recent2(p):
                    date_added = p.date_added
                    if date_added is None:
                        return datetime.min.replace(tzinfo=timezone.utc)
                    if isinstance(date_added, datetime) and date_added.tzinfo is None:
                        return date_added.replace(tzinfo=timezone.utc)
                    return date_added
                recent_photos = sorted(recent_photos, key=safe_date_added_recent2, reverse=True)
                filtered_photos = recent_photos[:max_photos]
                logger.info(f"使用最近添加的 {len(filtered_photos)} 张照片")

        # 限制数量（如果max_photos > 0）
        if max_photos > 0 and len(filtered_photos) > max_photos:
            filtered_photos = filtered_photos[:max_photos]
            logger.info(f"限制为 {max_photos} 张照片")
        elif max_photos == 0:
            logger.info(f"不限制数量，处理所有 {len(filtered_photos)} 张照片")

        # 转换为PhotoInfo对象
        photo_infos = []
        total = len(filtered_photos)

        for i, photo in enumerate(filtered_photos):
            if progress_callback:
                progress_callback("collecting", i + 1, total, f"Processing {photo.filename}")

            try:
                logger.info(f"🔄 处理照片 {i + 1}/{total}: {photo.filename} (UUID: {photo.uuid})")

                # 获取缩略图路径 - 使用多种方法尝试获取缩略图
                thumbnail_path = None

                # 方法1: 尝试使用 path_derivatives
                if hasattr(photo, "path_derivatives") and photo.path_derivatives:
                    logger.info(f"🔍 照片 {photo.filename} 有 {len(photo.path_derivatives)} 个衍生文件")

                    # 详细记录所有衍生文件信息
                    for idx, derivative_path in enumerate(photo.path_derivatives):
                        if derivative_path:
                            exists = os.path.exists(derivative_path)
                            if exists:
                                file_size = os.path.getsize(derivative_path)
                                logger.info(f"  ✅ 衍生文件 {idx + 1}: {derivative_path} (存在, {file_size} bytes)")
                            else:
                                logger.warning(f"  ❌ 衍生文件 {idx + 1}: {derivative_path} (不存在)")
                        else:
                            logger.warning(f"  ❌ 衍生文件 {idx + 1}: None")

                    # 选择第一个存在的衍生文件作为缩略图
                    for derivative_path in photo.path_derivatives:
                        if derivative_path and os.path.exists(derivative_path):
                            thumbnail_path = derivative_path
                            logger.info(f"✅ 使用衍生文件作为缩略图: {thumbnail_path}")
                            break

                    if not thumbnail_path:
                        logger.error(f"❌ 照片 {photo.filename} 有 {len(photo.path_derivatives)} 个衍生文件，但都不存在")
                else:
                    logger.warning(f"❌ 照片 {photo.filename} 没有衍生文件 (path_derivatives: {getattr(photo, 'path_derivatives', 'N/A')})")

                # 获取照片信息
                photo_info = PhotoInfo(
                    uuid=photo.uuid,
                    filename=photo.filename or f"photo_{photo.uuid[:8]}.jpg",
                    original_path=photo.path or "",
                    date_taken=_ensure_timezone_aware(photo.date),
                    file_size=photo.original_filesize or 0,
                    width=photo.width or 0,
                    height=photo.height or 0,
                    mime_type=photo.uti or "image/jpeg",
                    thumbnail_path=thumbnail_path,  # 添加缩略图路径
                )

                # 记录缩略图状态
                if thumbnail_path:
                    logger.info(f"✅ 照片 {photo.filename} 缩略图路径: {thumbnail_path}")
                else:
                    logger.warning(f"❌ 照片 {photo.filename} 没有可用的缩略图")

                photo_infos.append(photo_info)

            except Exception as e:
                logger.warning(f"处理照片失败 {photo.uuid}: {e}")
                continue

        logger.info(f"成功处理 {len(photo_infos)} 张照片")
        return photo_infos

    except Exception as e:
        logger.error(f"按日期范围收集照片失败: {e}")
        raise


def collect_photos_by_date_range_json(library_path: str, start_date: int, end_date: int, max_photos: int = 0, db_path: Optional[str] = None) -> str:
    """
    按日期范围收集照片并返回JSON字符串

    Args:
        library_path: Photos Library路径
        start_date: 开始日期的Unix时间戳（秒）
        end_date: 结束日期的Unix时间戳（秒）
        max_photos: 最大照片数量
        db_path: 可选的数据库路径

    Returns:
        str: 照片信息的JSON字符串
    """
    try:
        # 收集照片
        photos = collect_photos_by_date_range(library_path, start_date, end_date, max_photos, db_path)

        # Convert PhotoInfo objects to dictionaries
        photos_dict = [photo.to_dict() for photo in photos]

        # Return as JSON string
        return json.dumps(photos_dict, ensure_ascii=False, indent=None)

    except Exception as e:
        logger.error(f"Error in collect_photos_by_date_range_json: {e}")
        # Return empty list as JSON on error
        return json.dumps([])


def collect_photos_by_date_added_range_json(library_path: str, start_date: int, end_date: int, max_photos: int = 0, db_path: Optional[str] = None) -> str:
    """
    按添加日期范围收集照片并返回JSON字符串

    Args:
        library_path: Photos Library路径
        start_date: 开始日期的Unix时间戳（秒）
        end_date: 结束日期的Unix时间戳（秒）
        max_photos: 最大照片数量
        db_path: 可选的数据库路径

    Returns:
        str: 照片信息的JSON字符串
    """
    try:
        # 收集照片
        photos = collect_photos_by_date_added_range(library_path, start_date, end_date, max_photos, db_path)

        # Convert PhotoInfo objects to dictionaries
        photos_dict = [photo.to_dict() for photo in photos]

        # Return as JSON string
        return json.dumps(photos_dict, ensure_ascii=False, indent=None)

    except Exception as e:
        logger.error(f"Error in collect_photos_by_date_added_range_json: {e}")
        # Return empty list as JSON on error
        return json.dumps([])


def collect_photos_timeline_by_added_date_json(library_path: str, days_per_segment: int = 5, max_segments: int = 10, max_photos_per_segment: int = 50) -> str:
    """
    按添加时间线收集照片并返回JSON字符串

    Args:
        library_path: Photos Library路径
        days_per_segment: 每段的天数
        max_segments: 最大段数
        max_photos_per_segment: 每段最大照片数

    Returns:
        str: 照片信息的JSON字符串
    """
    try:
        # 收集照片
        photos = collect_photos_timeline_by_added_date(library_path, days_per_segment, max_segments, max_photos_per_segment)

        # Convert PhotoInfo objects to dictionaries
        photos_dict = [photo.to_dict() for photo in photos]

        # Return as JSON string
        return json.dumps(photos_dict, ensure_ascii=False, indent=None)

    except Exception as e:
        logger.error(f"Error in collect_photos_timeline_by_added_date_json: {e}")
        # Return empty list as JSON on error
        return json.dumps([])
